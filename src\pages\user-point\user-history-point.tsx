import { ArrowRightIcon, SmartPocketIcon } from '@/assets/icons';
import { Icon } from '@/components/core';
import PageTitle from '@/components/core/page-title';
import { Button } from '@/components/ui';
import { formatNumber } from '@/lib/helpers';
import { IPointHistory } from '@/services/user-point';
import dayjs from 'dayjs';

type Props = {
  setShowHistories: React.Dispatch<React.SetStateAction<boolean>>;
  data?: IPointHistory[];
};
export const UserHistoryPoint = ({ setShowHistories, data }: Props) => {
  return (
    <>
      <div className="flex gap-2 items-center">
        <Button
          className="rounded-full size-8 p-0 bg-white border-gray-9 rotate-180 mb-[12px]"
          variant="outline"
          type="button"
          onClick={() => setShowHistories(false)}
        >
          <img src={ArrowRightIcon} alt="icon" />
        </Button>
        <PageTitle title={'History'} />
      </div>
      <div className="flex flex-col gap-6 w-full h-[calc(100vh-142px)] overflow-y-auto border border-gray-9 bg-white rounded-[28px] p-7">
        <div className="w-full flex flex-col items-center">
          {data &&
            data.map((item: IPointHistory, index: number) => (
              <div
                key={index}
                className="py-4 flex gap-[18px] items-center w-full text-gray-900 font-medium"
              >
                <div
                  className={'rounded-full size-12 bg-orange-1 flex items-center justify-center'}
                >
                  <Icon src={SmartPocketIcon} width={24} height={24} />
                </div>
                <div className={'flex flex-col gap-1 max-w-[calc(100%-2*18px-48px-86px)]'}>
                  <span className="truncate" title={item.title}>
                    {item.title}
                  </span>
                  <span className="text-gray-600 text-xs font-medium">
                    {dayjs(item.created_at).format('YYYY/DD/MM')}
                  </span>
                </div>
                <div className={'flex-1 flex justify-end items-center whitespace-nowrap'}>
                  +{formatNumber(item.point)} PT
                </div>
              </div>
            ))}
        </div>
      </div>
    </>
  );
};
