import { useAuth } from '@/hooks/use-auth';
import NumberDisplay from '../shared/NumberDisplay';
import Link from 'next/link';
import { routePaths } from '@/constants/common';
import Image from 'next/image';
import { useGetTotalAirdropInfo } from '@/services/airdrop';
import clsx from 'clsx';

const HeaderTotalAirdrop = () => {
  const { isAuthenticated } = useAuth();
  const { data: airdropInfo, isLoading: isLoadingAirdropInfo } =
    useGetTotalAirdropInfo(isAuthenticated);
  return (
    <Link
      href={routePaths.airdrop}
      className="header-title flex gap-2 items-center text-primary-500 text-xs bg-primary-500/10 py-2 px-3.5 rounded-[22px]"
    >
      <span className="size-5 std:hidden">
        <Image src="/icons/small-star.svg" height={20} width={20} className="size-5" alt="icon" />
      </span>
      <div className={clsx('flex items-center gap-1.5', isLoadingAirdropInfo && 'animate-pulse')}>
        Total Airdrop
        <span className="text-base font-medium max-std:leading-none">
          <NumberDisplay
            number={airdropInfo && airdropInfo?.total ? airdropInfo.total.toString() : '???,???'}
          />
          &nbsp;SOL
        </span>
      </div>
    </Link>
  );
};

export default HeaderTotalAirdrop;
