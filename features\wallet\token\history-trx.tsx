import { cn, formatDateTime, formatNumberWithCommas, getHashUrl } from '@/utils/common';
import DepositIcon from '@/public/icons/deposit-icon.svg';
import SendIcon from '@/public/icons/send-token-icon.svg';
import { chainList, EChainId, isMainnet } from '@/lib/web3/constants';
import { useMemo, useState } from 'react';
import Image from 'next/image';
import { useTransferHistory } from '@/hooks/use-transfer-history';
import { useAuth } from '@/hooks/use-auth';
import { Address } from 'viem';
import LoadingAnimation from '@/components/shared/LoadingAnimation';
import { useSolUsdPrice } from '@/services/meme-launchpad';
import NumberDisplay from '@/components/shared/NumberDisplay';

export const chainListKeys = isMainnet ? [EChainId.Solana] : [EChainId.SolanaDevnet];

const customChainList = chainListKeys.map((chainId) => {
  const chain = chainList[chainId];
  return chain;
});

export const HistoryTrx = () => {
  const [chainId, setChainId] = useState<EChainId>(
    isMainnet ? EChainId.Solana : EChainId.SolanaDevnet
  );

  const { user } = useAuth();
  const address = useMemo(() => {
    if ([EChainId.Solana, EChainId.SolanaDevnet].includes(chainId)) {
      return user?.solana_address || '';
    }
    return user?.address || '';
  }, [user, chainId]);

  const { data: tokenPrice } = useSolUsdPrice();

  const { data: transferHistory, isFetching } = useTransferHistory(chainId, address as Address);

  return (
    <div className={'flex flex-col gap-4'}>
      {customChainList.length > 1 && (
        <div className={'overflow-x-auto hide-scrollbar'}>
          <div className={'flex gap-4 w-max'}>
            {customChainList.map((chain) => (
              <button
                key={chain.id}
                className={cn(
                  'flex gap-2 rounded-[40px] h-10 px-2 pr-4 items-center justify-center border',
                  chainId === chain.id
                    ? 'bg-[#EE811A1A] border-[#EE811A4D]'
                    : 'bg-white border-[#F0EEEE]'
                )}
                onClick={() => setChainId(chain.id)}
              >
                <Image src={chain.icon} alt={chain.name} width={24} height={24} />
                <span className={'text-sm text-[#666]'}>{chain.name}</span>
              </button>
            ))}
          </div>
        </div>
      )}
      <div>
        {isFetching && (
          <div className={'flex justify-center'}>
            <LoadingAnimation />
          </div>
        )}
        {!isFetching && !transferHistory?.length && (
          <div className={'flex flex-col py-3 gap-3 items-center justify-center'}>
            <span className={'text-gray-600'}>Coming Soon</span>
          </div>
        )}
        {!isFetching &&
          transferHistory?.map((trx) => (
            <div
              key={trx.hash}
              className={cn(
                'py-4 flex gap-[18px] items-center',
                'text-[#4C4C4C] font-medium',
                trx.hash && 'cursor-pointer'
              )}
              onClick={() => {
                trx.hash &&
                  chainList[chainId]?.blockExplorer &&
                  window.open(getHashUrl(trx.hash, chainId), '_blank');
              }}
            >
              <div>
                <div
                  className={'rounded-full size-12 bg-[#EE811A1A] flex items-center justify-center'}
                >
                  {trx.type === 'send' ? (
                    <SendIcon width={24} height={24} />
                  ) : (
                    <DepositIcon width={24} height={24} />
                  )}
                </div>
              </div>
              <div className={'flex flex-col gap-1'}>
                <div className={'flex gap-1'}>
                  <span className={'text-base'}>
                    {trx.type === 'send' ? `Send ${trx.symbol}` : `Get ${trx.symbol}`}
                  </span>
                  <Image
                    src={chainList[chainId].icon}
                    alt={chainList[chainId].name}
                    width={16}
                    height={16}
                  />
                </div>
                <span className={'text-gray-600 text-xs font-medium'}>
                  {formatDateTime(new Date(trx.createdAt), 'yyyy/MM/dd')}
                </span>
              </div>
              <div className={'flex-1 flex-col flex items-end'}>
                <span className={'text-base'}>
                  <NumberDisplay number={trx.value} maxDigits={6} /> {trx.symbol}
                </span>
                <span className={'text-xs font-medium text-gray-600'}>
                  $
                  {formatNumberWithCommas(
                    Math.abs(Number(trx.value) * (Number(tokenPrice?.priceUsd) || 0)).toFixed(3)
                  )}
                </span>
              </div>
            </div>
          ))}
      </div>
    </div>
  );
};
