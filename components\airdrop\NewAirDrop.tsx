'use client';
import Button, {
  BUTTON_SIZE,
  // BUTTON_VARIANT
} from '../shared/Button';
import Image from 'next/image';
import {
  formatAddress,
  formatNumber,
  getRaydiumDirectLink,
  openNewTabWithAnchor,
} from '@/utils/common';

import AirDropBg from '@/public/images/air-drop-background.png';
import QuestionMarkIcon from '@/public/icons/question-mark-icon.svg';
import RefreshIcon from '@/public/icons/refresh-icon.svg';
import { useAuth } from '@/hooks/use-auth';
import {
  EAirdropStatus,
  ISolAirdrop,
  useGetInfiniteCreatorAirdropList,
  useGetInfiniteSolAirdropList,
  useGetSolAirdropList,
  useGetTotalAirdropInfo,
  usePostClaimSolAirdrop,
} from '@/services/airdrop';
import LoadMoreButton from '../shared/LoadMoreButton';
import { format, formatDate } from 'date-fns';
import clsx from 'clsx';
import { toast } from 'react-toastify';
import React, { useEffect, useMemo, useState } from 'react';
import { useCountdown } from 'usehooks-ts';
import { useRouter, useSearchParams } from 'next/navigation';
import { routePaths } from '@/constants/common';
import { Tabs } from '../shared';
import FetchDataStatementWrapper from '../shared/FetchDataStatementWrapper';
import { useGetMyCreatorAirdrop } from './hooks/use-get-my-creator-airdrop';
import { useBondingCurveSDK } from '@/hooks/use-bonding-curve-sdk';
import { PublicKey } from '@solana/web3.js';
import SideMenu from '@/features/layout/SideMenu';
import DepositIcon from '@/public/icons/deposit-icon.svg';
import { useQueryClient } from '@tanstack/react-query';
import { EQueryKeys } from '@/services/query-keys';
import HeaderTitle from '@/features/layout/HeaderTitle';
import BigNumber from 'bignumber.js';
import PopUpModal from '../shared/PopUpModal';
import { useDisclosure } from '@/hooks/use-disclosure';
import DraggableDiv from '../shared/DraggableDiv';
import SvgCircle from '../shared/SvgCircle';
import RandomAvatar from '../account/RandomAvatar';
import { useIsFetching } from '@tanstack/react-query';
import RankIcon from '../shared/RankIcon';
import { useGetUserRank } from '@/services/auth';

const getBtnClass = (status: EAirdropStatus) => {
  switch (status) {
    case EAirdropStatus.SOON:
      return '!bg-gray-200 !text-gray-600';
    case EAirdropStatus.CHECK:
      return '!bg-gray-200 !text-gray-600';
    default:
      return '';
  }
};

const getStatusText = (status: EAirdropStatus) => {
  switch (status) {
    case EAirdropStatus.CLAIM:
      return 'Claim';
    case EAirdropStatus.CHECK:
      return 'Check';
    default:
      return 'Soon';
  }
};

export enum EAirdropTab {
  RANK = 'Rank',
  CREATOR = 'Creator',
}

const tabs = [
  { label: 'Rank', value: EAirdropTab.RANK },
  { label: 'Creator', value: EAirdropTab.CREATOR },
];

interface AirdropItemProps {
  icon?: string;
  progress?: number;
  title?: string;
  subTitle?: string;
  date?: string | Date;
  actionButton: React.ReactNode;
  onRedirect?: () => void;
  claimable?: boolean;
  isSoon?: boolean;
  isHistory?: boolean;
}

const MIN_SOL_CLAIMABLE = 0.0001; // Minimum amount to be claimable in SOL

const AirdropItem = ({
  icon,
  progress = 0,
  title,
  subTitle,
  date,
  actionButton,
  onRedirect,
  claimable = true,
  isSoon = false,
  isHistory = false,
}: AirdropItemProps) => {
  return (
    <div className="py-4 flex items-center justify-between gap-[18px]">
      {!isHistory ? (
        <div className="relative">
          <SvgCircle progress={progress} />
          <div
            className="absolute top-0 left-0 h-[60px] w-[60px] rounded-full flex flex-col items-center justify-center cursor-pointer"
            onClick={onRedirect}
          >
            {icon ? (
              <Image
                src={icon}
                alt={'Airdrop Logo'}
                width={48}
                height={48}
                className="rounded-full size-12 aspect-square object-cover"
              />
            ) : (
              <div className="rounded-full size-12 bg-gradient-to-b from-[#ffab00] to-[#ff4b00] flex justify-center items-center">
                <QuestionMarkIcon />
              </div>
            )}
          </div>
        </div>
      ) : (
        <div className={'rounded-full size-12 bg-[#EE811A1A] flex items-center justify-center'}>
          <DepositIcon width={24} height={24} />
        </div>
      )}
      <div className="grow flex items-center justify-between">
        <div className="flex flex-col gap-1.5">
          <span className="text-gray-900 font-medium cursor-pointer" onClick={onRedirect}>
            {title || '???'}
          </span>
          {!claimable && !isSoon && (
            <span className="text-sm input-text-gray-600-black font-medium">Not eligible</span>
          )}
          {(subTitle || date) && (
            <div className="flex items-center gap-1.5 input-text-gray-600-black font-medium">
              {subTitle && <span className="text-sm leading-4">{subTitle}</span>}
              {date && <span className="text-xs">{formatDate(date, 'dd MMM yyyy')}</span>}
            </div>
          )}
        </div>
        {actionButton}
      </div>
    </div>
  );
};

export default function NewAirDrop() {
  const router = useRouter();
  const queryClient = useQueryClient();

  const searchParams = useSearchParams();
  const initTab = searchParams.get('tab') || EAirdropTab.RANK;

  const { user, isAuthenticated } = useAuth();
  const { sdk, publicKey, connection } = useBondingCurveSDK();

  const [isOpenSideMenu, setIsOpenSideMenu] = useState(false);
  const [claimedCreatorAirdrop, setClaimedCreatorAirdrop] = useState<string[]>([]);

  const { data: canClaimTransferAirdrop, refetch: refetchCanClaimTransferAirdrop } =
    useGetSolAirdropList({
      page: 1,
      limit: 1,
      airdropStatusFilter: EAirdropStatus.CLAIM,
    });

  const [solAirdropFilterSelected, setSolAirdropFilterSelected] = useState<EAirdropStatus>(
    EAirdropStatus.CLAIM
  );
  const [creatorAirdropFilterSelected, setCreatorAirdropFilterSelected] = useState<EAirdropStatus>(
    EAirdropStatus.CLAIM
  );
  const [updatingIds, setUpdatingIds] = useState<Array<number | string>>([]);

  const [count, { startCountdown, stopCountdown, resetCountdown }] = useCountdown({
    countStart: 60, // 1 minutes in seconds
    intervalMs: 1000,
  });

  const unClaimablePopup = useDisclosure();

  const {
    data: solAirdrops,
    hasNextPage: hasNextSolAirdropPage,
    isLoading: isLoadingSolAirdrops,
    isFetching: isFetchingSolAirdrops,
    fetchNextPage: fetchNextSolAirdropPage,
    isRefetching: isRefetchingSolAirdrops,
    refetch: refetchSolAirdrops,
  } = useGetInfiniteSolAirdropList({ airdropStatusFilter: solAirdropFilterSelected });

  const {
    data: myCreatorAirdrops,
    isLoading: isLoadingMyCreatorAirdrops,
    refetch: refetchMyCreatorAirdrop,
    allMyCoins,
  } = useGetMyCreatorAirdrop();

  const {
    data: myClaimedCreatorAirdrops,
    isLoading: isLoadingClaimedCreatorAirdrops,
    hasNextPage: hasNextClaimedCreatorAirdropPage,
    fetchNextPage: fetchNextClaimedCreatorAirdropPage,
    isFetching: isFetchingClaimedCreatorAirdrops,
    refetch: refetchClaimedCreatorAirdrops,
    isRefetching: isRefetchingClaimedCreatorAirdrops,
  } = useGetInfiniteCreatorAirdropList();

  const { data: airdropInfo, isLoading: isLoadingAirdropInfo } = useGetTotalAirdropInfo();
  const { data: userRanking } = useGetUserRank(isAuthenticated);

  // Mutations
  const { mutateAsync: claimSolAirdrop, isPending: isClaimingSolAirdrop } =
    usePostClaimSolAirdrop();

  // Flattening data for easier access
  const flattenSolAirdrops = useMemo(() => {
    if (!solAirdrops || !solAirdrops.pages.length) return [];
    return solAirdrops.pages.flatMap((page) => page.data);
  }, [solAirdrops]);

  const flattenClaimedCreatorAirdrops = useMemo(() => {
    if (!myClaimedCreatorAirdrops) return [];
    return myClaimedCreatorAirdrops.pages.flatMap((page) => page.data);
  }, [myClaimedCreatorAirdrops]);

  const isLoadedSolAirdrops =
    !isLoadingSolAirdrops && !isRefetchingSolAirdrops && !isFetchingSolAirdrops;

  useEffect(() => {
    if (count <= 0) {
      stopCountdown();
      if (initTab === EAirdropTab.RANK) {
        refetchSolAirdrops();
      } else {
        refetchClaimedCreatorAirdrops();
      }
    }
  }, [count, initTab, refetchSolAirdrops, stopCountdown]);

  const refetchData = () => {
    resetCountdown();
    startCountdown();
    if (initTab === EAirdropTab.RANK) {
      refetchSolAirdrops();
      refetchCanClaimTransferAirdrop();
    } else {
      refetchMyCreatorAirdrop();
    }
  };

  useEffect(() => {
    if (isLoadedSolAirdrops) {
      resetCountdown();
      startCountdown();
    }
  }, [isLoadedSolAirdrops, resetCountdown, startCountdown]);

  const redirect = (status: EAirdropStatus, tokenAddress?: string) => {
    if (status === EAirdropStatus.SOON && tokenAddress) {
      router.push(routePaths.memePadDetail(tokenAddress || ''));
      return;
    }

    if (status === EAirdropStatus.CHECK) {
      router.push(routePaths.wallet + `?tab=token`);
      return;
    }

    if (tokenAddress) openNewTabWithAnchor(getRaydiumDirectLink(tokenAddress));
  };

  const handleClaimSolAirdrop = async (airdrop: ISolAirdrop) => {
    const { id, status } = airdrop;
    if (status === EAirdropStatus.CHECK) {
      redirect(status);
      return;
    }

    try {
      setUpdatingIds((prev) => [...prev, id]);
      await claimSolAirdrop(id);
      refetchSolAirdrops();
      refetchCanClaimTransferAirdrop();
      toast.success('Successfully claimed!');
    } catch (error) {
      const errorMessage =
        typeof error === 'object' && error !== null && 'message' in error
          ? (error as { message?: string }).message
          : 'Error claiming airdrop';
      toast.error(errorMessage || `Claim fail!`);
    } finally {
      setUpdatingIds((prev) => prev.filter((item) => item !== id));
    }
  };

  const handleWithdrawCreatorAirdrop = async (mint: string) => {
    try {
      setUpdatingIds((prev) => [...prev, 'creator' + mint]);
      const func = await sdk?.withdrawCreatorFeeTx(new PublicKey(mint), publicKey!);

      if (!func) {
        toast.error('Failed to create transaction function');
        return;
      }
      const latestBlockhash = await connection.getLatestBlockhash();
      const transaction = await func.transaction();
      transaction.recentBlockhash = latestBlockhash.blockhash;
      transaction.feePayer = publicKey!;

      const { signature } = await window.solana.signAndSendTransaction(transaction as any);

      await connection.getSignatureStatus(signature);

      toast.success('Successfully claimed!');
      setClaimedCreatorAirdrop((prev) => [...prev, mint]);
      setTimeout(() => {
        queryClient.invalidateQueries({
          queryKey: [EQueryKeys.CREATOR_AIRDROP_LIST_INFINITE],
        });
      }, 1000);
      setTimeout(() => {
        queryClient.invalidateQueries({
          queryKey: [EQueryKeys.TOTAL_AIRDROP_INFO],
        });
      }, 2500);
    } catch (error) {
      const errorMessage =
        typeof error === 'object' && error !== null && 'message' in error
          ? (error as { message?: string }).message
          : 'Error withdrawing creator airdrop';
      toast.error(errorMessage || 'Claim failed!');
    } finally {
      setUpdatingIds((prev) => prev.filter((item) => item !== 'creator' + mint));
      setTimeout(() => refetchMyCreatorAirdrop(), 2000);
    }
  };

  const params = new URLSearchParams(searchParams.toString());

  const updateSearchParams = (key: string, value: string) => {
    params.set(key, value);
    router.push(`${window.location.pathname}?${params.toString()}`);
  };

  const filteredCreatorAirdrops = useMemo(() => {
    if (!myCreatorAirdrops) return [];
    return myCreatorAirdrops.filter(
      (airdrop) => !claimedCreatorAirdrop.some((address) => airdrop.tokenAddress === address)
    );
  }, [myCreatorAirdrops, claimedCreatorAirdrop]);

  const totalRankAirdrops = useMemo(() => {
    const solTransferAirdropCount = canClaimTransferAirdrop
      ? canClaimTransferAirdrop.pagination.total
      : 0;
    return solTransferAirdropCount;
  }, [canClaimTransferAirdrop]);

  const totalCreatorAirdrops = useMemo(() => {
    const myCreatorAirdropsCount = filteredCreatorAirdrops ? filteredCreatorAirdrops.length : 0;
    return myCreatorAirdropsCount;
  }, [filteredCreatorAirdrops]);

  const onUnClaimableClick = () => {
    unClaimablePopup.open();
  };

  const isFetchingProfile = useIsFetching({
    queryKey: [EQueryKeys.PROFILE],
  });

  return (
    <>
      <HeaderTitle title="Airdrop" hiddenBack />
      <div
        className="z-0 w-full h-60 absolute top-0 std:rounded-t-std overflow-hidden"
        style={{
          backgroundImage: `url(${AirDropBg.src})`,
          backgroundRepeat: 'no-repeat',
          backgroundSize: 'cover',
          zIndex: '1',
        }}
      >
        <div
          className="absolute w-full h-[148px] bottom-0"
          style={{
            background:
              'linear-gradient(180deg, rgba(245, 245, 245, 0) 0%, rgba(245, 245, 245, 0.6) 48.76%, #F5F5F5 96.56%)',
          }}
        ></div>
      </div>
      <div className="relative z-10 py-5 px-[18px] std:pb-0 token-slide overflow-hidden flex flex-col h-full gap-[18px] items-center">
        <DraggableDiv className="flex flex-col gap-3 w-full overflow-auto hide-scrollbar max-std:bg-black/30 max-std:px-[18px] max-std:py-4 max-std:rounded-[20px]">
          <div className="w-full flex items-center gap-[18px] std:hidden">
            {user?.display_icon ? (
              <Image
                src={user?.display_icon}
                alt="avatar"
                width={40}
                height={40}
                className="size-10 object-cover rounded-full border border-gray-200"
              />
            ) : (
              <RandomAvatar className="size-10 object-cover rounded-full border border-gray-200" />
            )}
            <div className="w-full max-w-[calc(100%-40px-18px)] flex justify-between">
              <div className="flex flex-col gap-1 max-w-[calc(100%-40px-18px-20px)]">
                <span
                  className={clsx(
                    'font-medium text-white truncate',
                    isFetchingProfile && 'animate-pulse'
                  )}
                >
                  {user?.display_name || user?.uuid || '?????????'}
                </span>
                {user?.created_at && (
                  <span className="text-xs font-medium text-white">
                    Created Account : {format(user?.created_at, 'dd MMM yyyy')}
                  </span>
                )}
              </div>
              <RankIcon rank={userRanking?.rankName} width={20} height={20} />
            </div>
          </div>
          <div className="w-full border-b border-[#FFFFFF33] std:hidden" />
          <div className="w-full flex gap-5">
            <div className="flex-1 std:bg-black/30 rounded-std min-w-fit max-std:w-fit flex std:px-[18px] std:py-4 gap-[18px] items-center">
              <div className="relative max-std:hidden">
                <SvgCircle progress={100} />
                <div className="absolute top-0 left-0 h-[60px] w-[60px] rounded-full flex flex-col items-center justify-center">
                  <Image
                    src={'/icons/chain-sol.svg'}
                    alt={'Airdrop Logo'}
                    width={48}
                    height={48}
                    className="rounded-full size-12 aspect-square object-cover"
                  />
                </div>
              </div>
              <div className="w-full flex flex-col max-std:items-center gap-1.5">
                <span className="font-medium text-white">Total Rank Fee</span>
                <span className="w-full bg-white/20 px-[14px] std:py-1.5 py-4 std:rounded-lg rounded-xl std:text-sm text-xl max-std:leading-none text-white whitespace-nowrap">
                  <div
                    className={clsx(
                      'flex items-center gap-1.5',
                      isLoadingAirdropInfo && 'animate-pulse'
                    )}
                  >
                    {airdropInfo?.totalRankFee ? formatNumber(airdropInfo?.totalRankFee, 6) : '-'}
                    &nbsp;SOL
                  </div>
                </span>
              </div>
            </div>
            <div className="flex-1 std:bg-black/30 rounded-std min-w-fit max-std:w-fit flex std:px-[18px] std:py-4 gap-[18px] items-center">
              <div className="relative max-std:hidden">
                <SvgCircle progress={100} />
                <div className="absolute top-0 left-0 h-[60px] w-[60px] rounded-full flex flex-col items-center justify-center">
                  <Image
                    src={'/icons/chain-sol.svg'}
                    alt={'Airdrop Logo'}
                    width={48}
                    height={48}
                    className="rounded-full size-12 aspect-square object-cover"
                  />
                </div>
              </div>
              <div className="w-full flex flex-col max-std:items-center gap-1.5">
                <span className="font-medium text-white">Total Creator Fee</span>
                <span className="w-full bg-white/20 px-[14px] std:py-1.5 py-4 std:rounded-lg rounded-xl std:text-sm text-xl max-std:leading-none text-white whitespace-nowrap">
                  <div
                    className={clsx(
                      'flex items-center gap-1.5',
                      isLoadingAirdropInfo && 'animate-pulse'
                    )}
                  >
                    {airdropInfo?.totalCreatorFee
                      ? formatNumber(airdropInfo?.totalCreatorFee, 6)
                      : '-'}
                    &nbsp;SOL
                  </div>
                </span>
              </div>
            </div>
          </div>
        </DraggableDiv>

        <div className="w-full grow flex flex-col gap-2.5 px-5 py-6 bg-white rounded-std">
          <div className={'bg-gray-300 rounded-2xl gap-2 flex px-2 py-1.5'}>
            {tabs.map((tab) => (
              <button
                key={tab.value}
                className={clsx(
                  'flex-1 flex items-center justify-center text-gray-800 font-medium py-2 gap-2.5',
                  initTab === tab.value && 'bg-white rounded-xl'
                )}
                onClick={() => {
                  updateSearchParams('tab', tab.value);
                }}
              >
                {tab.label}
                <span className="py-1.5 px-2 rounded-full bg-primary-500 text-white min-w-6 text-xs leading-[10px]">
                  {tab.value === EAirdropTab.RANK ? totalRankAirdrops : totalCreatorAirdrops}
                </span>
              </button>
            ))}
          </div>
          <div className="w-full flex justify-between">
            <div
              className="flex items-center gap-2 rounded-full border pl-2.5 pr-4 py-2 cursor-pointer w-fit border-gray-200"
              onClick={() => {
                setIsOpenSideMenu(true);
              }}
            >
              <Image
                src="/icons/chain-sol.svg"
                alt="chain icon"
                width={24}
                height={24}
                className="size-6 aspect-square"
              />
              <span className="text-sm text-gray-800">
                {user?.solana_address ? formatAddress(user?.solana_address, 6) : '???'}
              </span>
            </div>
            <div
              className="ml-auto flex items-center gap-1 input-text-gray-600-black text-xs cursor-pointer"
              onClick={() => refetchData()}
            >
              <RefreshIcon /> {count} s
            </div>
          </div>
          {initTab === EAirdropTab.RANK && (
            <div className="flex flex-col gap-[18px]">
              <div className="w-full flex flex-col gap-3 max-w-full overflow-hidden">
                <div className="w-full">
                  <Tabs
                    activeTab={solAirdropFilterSelected}
                    classNameContainer="overflow-x-auto hide-scrollbar w-fit"
                    classNameContent="!mt-0"
                    labelClassName="flex-shrink-0 border border-gray-200 rounded-full"
                    textClassName="px-5"
                    onChange={(tab: string) => {
                      setSolAirdropFilterSelected(tab as EAirdropStatus);
                    }}
                    tabs={[
                      { label: 'Airdrop', value: EAirdropStatus.CLAIM, content: <></> },
                      { label: 'History', value: EAirdropStatus.CHECK, content: <></> },
                    ]}
                  />
                  <div className="flex flex-col w-full">
                    <FetchDataStatementWrapper
                      isLoading={isLoadingSolAirdrops || isRefetchingSolAirdrops}
                      isEmptyData={!flattenSolAirdrops.length}
                      customNotDataComponent={
                        <AirdropItem
                          title="No items found"
                          isSoon
                          actionButton={
                            <Button
                              size={BUTTON_SIZE.SMALL}
                              className={clsx(
                                'rounded-full !px-4',
                                getBtnClass(EAirdropStatus.SOON)
                              )}
                              disabled
                            >
                              {getStatusText(EAirdropStatus.SOON)}
                            </Button>
                          }
                        />
                      }
                    >
                      <div className="w-full max-h-[500px] overflow-auto">
                        {flattenSolAirdrops.map((airdrop) => {
                          const { status } = airdrop;
                          const isHistory = status === EAirdropStatus.CHECK;
                          const isUnClaimable = new BigNumber(airdrop.sol_amount).lte(
                            new BigNumber(MIN_SOL_CLAIMABLE)
                          );
                          return (
                            <AirdropItem
                              key={airdrop.id}
                              progress={100}
                              icon="/icons/chain-sol.svg"
                              title="Rank Fee"
                              subTitle={
                                isHistory
                                  ? formatDate(airdrop.created_at, 'dd MMM yyyy')
                                  : formatNumber(airdrop.sol_amount, 9)
                              }
                              actionButton={
                                isHistory ? (
                                  <span className="text-gray-900 font-medium">{`+${formatNumber(airdrop.sol_amount, 9)} SOL`}</span>
                                ) : (
                                  <div className="relative">
                                    <Button
                                      size={BUTTON_SIZE.SMALL}
                                      className={clsx('rounded-full !px-4', getBtnClass(status))}
                                      onClick={() => handleClaimSolAirdrop(airdrop)}
                                      isLoading={
                                        isClaimingSolAirdrop &&
                                        updatingIds.includes(airdrop?.id || 0)
                                      }
                                      disabled={isUnClaimable}
                                    >
                                      {getStatusText(status)}
                                    </Button>
                                    {isUnClaimable && (
                                      <div
                                        className="absolute w-full h-full top-0 left-0"
                                        onClick={onUnClaimableClick}
                                      ></div>
                                    )}
                                  </div>
                                )
                              }
                              isHistory={isHistory}
                            />
                          );
                        })}
                        <div className="flex justify-center w-full">
                          <LoadMoreButton
                            hasNextPage={!!hasNextSolAirdropPage}
                            isFetching={isFetchingSolAirdrops}
                            fetchNextPage={fetchNextSolAirdropPage}
                          >
                            View More
                          </LoadMoreButton>
                        </div>
                      </div>
                    </FetchDataStatementWrapper>
                  </div>
                </div>
              </div>
            </div>
          )}
          {initTab === EAirdropTab.CREATOR && (
            <div className="w-full flex flex-col gap-3 max-w-full overflow-hidden">
              <div className="w-full">
                <Tabs
                  activeTab={creatorAirdropFilterSelected}
                  classNameContainer="overflow-x-auto hide-scrollbar w-fit"
                  classNameContent="!mt-0"
                  labelClassName="flex-shrink-0 border border-gray-200 rounded-full"
                  textClassName="px-5"
                  onChange={(tab: string) => {
                    setCreatorAirdropFilterSelected(tab as EAirdropStatus);
                  }}
                  tabs={[
                    { label: 'Airdrop', value: EAirdropStatus.CLAIM, content: <></> },
                    { label: 'History', value: EAirdropStatus.CHECK, content: <></> },
                  ]}
                />
                <div className="flex flex-col w-full">
                  <FetchDataStatementWrapper
                    isLoading={
                      creatorAirdropFilterSelected === EAirdropStatus.CLAIM
                        ? isLoadingMyCreatorAirdrops
                        : isLoadingClaimedCreatorAirdrops || isRefetchingClaimedCreatorAirdrops
                    }
                    isEmptyData={
                      creatorAirdropFilterSelected === EAirdropStatus.CLAIM
                        ? !filteredCreatorAirdrops.length
                        : !flattenClaimedCreatorAirdrops.length
                    }
                    customNotDataComponent={
                      <AirdropItem
                        title="No items found"
                        isSoon
                        actionButton={
                          <Button
                            size={BUTTON_SIZE.SMALL}
                            className={clsx('rounded-full !px-4', getBtnClass(EAirdropStatus.SOON))}
                            disabled
                          >
                            {getStatusText(EAirdropStatus.SOON)}
                          </Button>
                        }
                      />
                    }
                  >
                    <div className="w-full">
                      {[EAirdropStatus.CLAIM].includes(creatorAirdropFilterSelected) &&
                        filteredCreatorAirdrops.map((airdrop) => {
                          const isUnClaimable = new BigNumber(airdrop.balance).lte(
                            new BigNumber(MIN_SOL_CLAIMABLE)
                          );
                          return (
                            <AirdropItem
                              key={'creator-' + airdrop.tokenAddress}
                              progress={100}
                              icon={airdrop.iconUri}
                              title={`$${airdrop.symbol}`}
                              subTitle={`${formatNumber(airdrop.balance)} SOL`}
                              actionButton={
                                <div className="relative">
                                  <Button
                                    size={BUTTON_SIZE.SMALL}
                                    className={clsx(
                                      'rounded-full !px-4',
                                      getBtnClass(EAirdropStatus.CLAIM)
                                    )}
                                    onClick={() =>
                                      handleWithdrawCreatorAirdrop(airdrop.tokenAddress)
                                    }
                                    isLoading={updatingIds.includes(
                                      'creator' + airdrop.tokenAddress
                                    )}
                                    disabled={isUnClaimable}
                                  >
                                    {getStatusText(EAirdropStatus.CLAIM)}
                                  </Button>
                                  {isUnClaimable && (
                                    <div
                                      className="absolute w-full h-full top-0 left-0"
                                      onClick={onUnClaimableClick}
                                    ></div>
                                  )}
                                </div>
                              }
                            />
                          );
                        })}
                      {[EAirdropStatus.CHECK].includes(creatorAirdropFilterSelected) &&
                        flattenClaimedCreatorAirdrops?.map((airdrop) => {
                          const coin = allMyCoins?.find(
                            (item) => item.tokenAddress === airdrop.token_address
                          );
                          return (
                            <AirdropItem
                              key={airdrop.token_address}
                              progress={0}
                              icon="/icons/chain-sol.svg"
                              title={`$${coin?.symbol}`}
                              subTitle={formatDate(airdrop.created_at, 'dd MMM yyyy')}
                              actionButton={
                                <span className="text-gray-900 font-medium">{`+${formatNumber(airdrop.sol_amount, 4)} SOL`}</span>
                              }
                              isHistory
                            />
                          );
                        })}
                      {creatorAirdropFilterSelected === EAirdropStatus.CHECK && (
                        <div className="flex justify-center w-full">
                          <LoadMoreButton
                            hasNextPage={!!hasNextClaimedCreatorAirdropPage}
                            isFetching={isFetchingClaimedCreatorAirdrops}
                            fetchNextPage={fetchNextClaimedCreatorAirdropPage}
                          >
                            View More
                          </LoadMoreButton>
                        </div>
                      )}
                    </div>
                  </FetchDataStatementWrapper>
                </div>
              </div>
            </div>
          )}
        </div>
        {initTab === EAirdropTab.CREATOR && (
          <p className="input-text-gray-600-black text-xs">
            This reward is for custom artwork or creative services.
          </p>
        )}
      </div>
      <SideMenu isOpen={isOpenSideMenu} setIsOpen={setIsOpenSideMenu} isNativeApp />
      <PopUpModal
        isOpen={unClaimablePopup.opened}
        onClose={unClaimablePopup.close}
        className="!pb-5"
      >
        <div className="flex flex-col gap-6">
          <p className="text-gray-800 text-center">
            You can’t claim right now. Please wait until the token price is high enough to cover the
            transaction fee.
          </p>
          <Button className="!w-full" onClick={unClaimablePopup.close}>
            Close
          </Button>
        </div>
      </PopUpModal>
    </>
  );
}
