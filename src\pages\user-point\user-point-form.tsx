import { InputField, NumberInputField } from '@/components/core';
import ActionButton from '@/components/core/action-button';
import PageTitle from '@/components/core/page-title';
import { ToggleSwitch } from '@/components/core/toggle-switch';
import { Button } from '@/components/ui';
import { OPEN_FORM } from '@/constants';
import { useDisclosure, useQueryString } from '@/hooks';
import {
  useGetUserPointByAddress,
  useUpdateUserPoint,
} from '@/services/user-point/user-point.queries';
import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect, useRef, useState } from 'react';
import { Controller, FormProvider, useForm } from 'react-hook-form';
import { toast } from 'react-toastify';
import Modal from '@/components/core/modal';
import { MAX_NUMBER_INPUT } from '@/constants/common';
import { z } from 'zod';
import { UserHistoryPoint } from './user-history-point';

const defaultValues = {
  address: '',
  sp_point: 0,
  ga_point: 0,
  is_limit_invite: false,
};

const UserPointForm = () => {
  const { queryString, setQueryString } = useQueryString();
  const id = queryString.id;
  const [showHistories, setShowHistories] = useState(false);
  const giveawayPointModal = useDisclosure();
  const formRef = useRef(null);
  const schema = z
    .object({
      uuid: z.string(),
      ga_point: z.number(),
      point: z.number(),
      address: z.string().trim(),
      // .regex(addressRegex, 'Invalid address.'),
      is_limit_invite: z.boolean(),
    })
    .refine(
      (data) => {
        if (data.ga_point <= 0 && giveawayPointModal.opened) return false;
        return true;
      },
      { message: 'Amount must be greater than 0', path: ['ga_point'] }
    );
  type UserPointSchemaFormType = z.infer<typeof schema>;

  const methods = useForm<UserPointSchemaFormType>({
    resolver: zodResolver(schema),
    defaultValues,
  });
  useEffect(() => {
    setShowHistories(false);
  }, [id]);
  const { handleSubmit, reset, control, register } = methods;

  const { data, error } = useGetUserPointByAddress(id!);
  const { mutateAsync: update, isPending: isUpdating } = useUpdateUserPoint(id);

  useEffect(() => {
    if (error?.message === 'Not Found.') {
      setQueryString({
        id: '',
        [OPEN_FORM]: 'false',
      });
    } else if (!id) {
      reset(defaultValues);
    } else if (data) {
      reset({
        point: data.user_ranking.point,
        ga_point: 0,
        address: data.solana_address ?? '',
        uuid: data.uuid,
        is_limit_invite: data.is_limit_invite,
      });
    }
  }, [data, error?.message, id, reset, setQueryString]);

  const onSubmit = (values: UserPointSchemaFormType) => {
    update({
      id: Number(id),
      address: values.address,
      point: values.ga_point ?? 0,
      is_limit_invite: values.is_limit_invite,
    }).then(() => {
      if (values.ga_point) toast.success('Giveaway Point successfully');
      else toast.success('Updated successfully.');

      giveawayPointModal.close();
    });
  };
  if (showHistories)
    return <UserHistoryPoint setShowHistories={setShowHistories} data={data?.point_history} />;
  return (
    <FormProvider {...methods}>
      <form onSubmit={handleSubmit(onSubmit)} ref={formRef}>
        <PageTitle
          title={'Detail'}
          rightButton={<ActionButton actionType={'submit'} disabled={isUpdating} />}
        />
        <div className="flex flex-col gap-6 w-full h-[calc(100vh-142px)] overflow-y-auto border border-gray-9 bg-white rounded-[28px] p-7">
          <InputField
            id={'address'}
            label="Solana Address"
            name="address"
            placeholder="0x...0000"
            disabled
          />
          {/* <Controller
            name={'sp_point'}
            control={control}
            render={({ field, fieldState: { error } }) => (
              <NumberInputField
                label={'Point Amount'}
                placeholder={'1'}
                numberValue={Number(field.value)}
                onChangeValue={(e) => field.onChange(Number(e))}
                error={error?.message}
                allowDecimal={false}
                maxNumbers={MAX_NUMBER_INPUT}
              />
            )}
          /> */}
          <div className="flex flex-col gap-2">
            <InputField
              id={'point'}
              label="Point Ranking"
              name="point"
              value={data?.user_ranking.point}
              disabled
            />
            <div className="flex gap-2">
              <Button
                type="button"
                className="rounded-[14px] h-9 flex flex-col justify-center items-center text-sm font-semibold"
                variant="primary"
                onClick={() => giveawayPointModal.open()}
              >
                Point Giveaway
              </Button>
              <Button
                type="button"
                className="rounded-[14px] h-9 flex flex-col justify-center items-center text-sm font-semibold"
                variant="primary"
                onClick={() => setShowHistories(true)}
              >
                Point Histories
              </Button>
            </div>
          </div>
          <InputField id={'uuid'} {...register('uuid')} label="UUID" disabled />
          <InputField
            id={'referCode'}
            label="User Ref Code"
            name="ref_code"
            value={data?.ref_code}
            disabled
          />
          <div className="flex gap-x-4 items-center font-gray-5 text-sm text-gray-5 my-1">
            <span>Invite limited</span>
            <Controller
              name="is_limit_invite"
              control={control}
              render={({ field }) => (
                <ToggleSwitch
                  id={'is_limit_invite'}
                  value={field.value}
                  onChange={(val) => field.onChange(val)}
                />
              )}
            />
          </div>
        </div>
        <Modal
          isOpen={giveawayPointModal.opened}
          onClose={giveawayPointModal.close}
          container={formRef.current!}
        >
          <PageTitle title="Point Giveaway" />
          <div className="flex flex-col justify-end gap-2">
            <Controller
              name={'ga_point'}
              control={control}
              render={({ field, fieldState: { error } }) => (
                <NumberInputField
                  label={'Point Amount'}
                  placeholder={'1'}
                  numberValue={Number(field.value)}
                  onChangeValue={(e) => field.onChange(Number(e))}
                  error={error?.message}
                  allowDecimal={false}
                  maxNumbers={MAX_NUMBER_INPUT}
                />
              )}
            />
            <Button
              type="submit"
              className="rounded-[14px] h-9 flex flex-col justify-center items-center text-sm font-semibold"
              variant="primary"
              disabled={isUpdating}
            >
              Finish
            </Button>
          </div>
        </Modal>
      </form>
    </FormProvider>
  );
};

export default UserPointForm;
