// import { addressRegex } from '@/constants/common';
// import { z } from 'zod';

// export const userPointSchema = z.object({
//   uuid: z.string(),
//   ga_point:z.number().optional(),
//   point: z.number(),
//   address: z.string().trim(),
//   // .regex(addressRegex, 'Invalid address.'),
//   is_limit_invite: z.boolean(),
// });

// export type UserPointSchemaFormType = z.infer<typeof userPointSchema>;
