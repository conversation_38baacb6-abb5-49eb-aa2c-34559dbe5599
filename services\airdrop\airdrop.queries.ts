import { useInfiniteQuery, useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { EQueryKeys } from '../query-keys';
import { AirdropServices } from './airdrop.service';
import { IListAirdropParams } from './airdrop.types';

export const useGetAirdropList = (params?: IListAirdropParams) => {
  return useQuery({
    queryKey: [EQueryKeys.AIRDROP_LIST, { params }],
    queryFn: () => AirdropServices.getAirdropList({ page: 1, limit: 10, ...params }),
  });
};

export const useGetInfiniteAirdropList = (params?: IListAirdropParams) => {
  return useInfiniteQuery({
    queryKey: [EQueryKeys.AIRDROP_LIST_INFINITE, ...(params ? Object.entries(params) : [])],
    queryFn: async ({ pageParam }) => {
      const response = await AirdropServices.getAirdropList({
        ...params,
        page: pageParam,
        limit: 10,
      });
      return response;
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      return lastPage.nextPage;
    },
  });
};

export const useGetSolPriceAirdrop = (id: string[]) => {
  return useQuery({
    queryKey: [EQueryKeys.SOL_PRICE_AIRDROP, id.length],
    queryFn: () => AirdropServices.getSolPriceAirdrop(id),
    enabled: id.length > 0,
  });
};

export const useGetSolAirdropList = (params?: IListAirdropParams) => {
  return useQuery({
    queryKey: [EQueryKeys.SOL_AIRDROP_LIST, ...(params ? Object.entries(params) : [])],
    queryFn: () =>
      AirdropServices.getSolAirdropList({
        ...params,
        page: 1,
        limit: 10,
      }),
  });
};

export const useGetInfiniteSolAirdropList = (params?: IListAirdropParams) => {
  return useInfiniteQuery({
    queryKey: [EQueryKeys.SOL_AIRDROP_LIST_INFINITE, ...(params ? Object.entries(params) : [])],
    queryFn: async ({ pageParam }) => {
      const response = await AirdropServices.getSolAirdropList({
        ...params,
        page: pageParam,
        limit: 10,
      });
      return response;
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      return lastPage.pagination.next;
    },
  });
};

export const useGetAirdropDetail = (airdropId: string) => {
  return useQuery({
    queryKey: [EQueryKeys.AIRDROP_DETAIL, airdropId],
    queryFn: () => AirdropServices.getAirdropDetail(airdropId),
    enabled: !!airdropId,
  });
};

export const useUpdateAirdropStatus = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (id: number) => {
      return AirdropServices.putUpdateStatusAirdrop(id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [EQueryKeys.AIRDROP_LIST, EQueryKeys.AIRDROP_LIST_INFINITE],
      });
    },
  });
};

export const usePostClaimAirdrop = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (id: number) => {
      return AirdropServices.postClaimAirdrop(id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [EQueryKeys.AIRDROP_LIST, EQueryKeys.AIRDROP_LIST_INFINITE],
      });
      queryClient.invalidateQueries({
        queryKey: [EQueryKeys.TOTAL_AIRDROP_INFO],
      });
    },
  });
};

export const usePostClaimSolAirdrop = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (id: number) => {
      return AirdropServices.postClaimSolAirdrop(id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [EQueryKeys.SOL_AIRDROP_LIST_INFINITE],
      });
      queryClient.invalidateQueries({
        queryKey: [EQueryKeys.TOTAL_AIRDROP_INFO],
      });
    },
  });
};

export const useGetSummaryPoint = (enabled: boolean) => {
  return useQuery({
    queryKey: [EQueryKeys.SUMMARY_POINT],
    queryFn: () => AirdropServices.getSummaryPoint(),
    refetchOnWindowFocus: false,
    enabled,
  });
};

export const useGetInfiniteCreatorAirdropList = () => {
  return useInfiniteQuery({
    queryKey: [EQueryKeys.CREATOR_AIRDROP_LIST_INFINITE],
    queryFn: async ({ pageParam }) => {
      const response = await AirdropServices.getClaimedCreatorAirdrop({
        page: pageParam,
        limit: 10,
      });
      return response;
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      return lastPage.pagination.next;
    },
  });
};

export const useGetTotalAirdropInfo = (enabled: boolean = true) => {
  return useQuery({
    queryKey: [EQueryKeys.TOTAL_AIRDROP_INFO],
    queryFn: () => AirdropServices.getTotalAirdropInfo(),
    refetchOnWindowFocus: false,
    enabled,
  });
};
