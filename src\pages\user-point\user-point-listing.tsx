import { ArrowRightIcon } from '@/assets/icons';
import { LoadingScreen } from '@/components/core';
import EmptyData from '@/components/core/empty-data';
import PageTitle from '@/components/core/page-title';
import { SearchInputField } from '@/components/core/search-input-field';
import { OPEN_FORM } from '@/constants';
import { EPaginationDefault } from '@/constants/common';
import { useQueryString } from '@/hooks';
import useScroll from '@/hooks/use-scroll';
import { useSearch } from '@/hooks/use-search';
import { cn, formatAddress } from '@/lib/utils';
import { IUserPoint } from '@/services/user-point';
import { useGetUserPoint } from '@/services/user-point/user-point.queries';
import { useMemo } from 'react';

const UserPoint = () => {
  const { handleSearch } = useSearch('search');
  const { queryString, setQueryString } = useQueryString();
  const { data, isLoading, hasNextPage, fetchNextPage, isFetchingNextPage } = useGetUserPoint({
    search: queryString.search || '',
    page: EPaginationDefault.PAGE,
    perPage: EPaginationDefault.PER_PAGE,
  });

  const { handleScroll } = useScroll(fetchNextPage, hasNextPage, isFetchingNextPage);

  const userPoint = useMemo(() => {
    return data?.pages.flatMap((page) => page.data);
  }, [data]);

  const toggleItem = (item_id: string) => {
    const id = item_id === queryString.address ? '' : item_id;
    setQueryString({
      id,
      [OPEN_FORM]: !id && queryString[OPEN_FORM] ? 'false' : 'true',
    });
  };

  const renderList = () => {
    if (isLoading) {
      return <LoadingScreen />;
    }
    if (userPoint && userPoint.length > 0) {
      return (
        <>
          <div className="text-base text-gray-7 font-medium px-4 py-4">User Solana Address</div>
          {userPoint.map((item: IUserPoint) => {
            const isActive = item.address === queryString.address;
            const address = item.solana_address && formatAddress(item.solana_address || '');
            return (
              <div
                key={item.id}
                onClick={() => toggleItem(String(item.id))}
                className={cn(
                  'flex justify-between items-center cursor-pointer border-t border-gray-9  font-medium text-sm pt-3 px-4 py-[10px] h-[60px]',
                  isActive ? 'text-orange-1' : 'text-gray-5'
                )}
              >
                <div className="truncate  whitespace-nowrap overflow-hidden">{address}</div>
                <img src={ArrowRightIcon} alt="" />
              </div>
            );
          })}
        </>
      );
    }
    return <EmptyData />;
  };

  return (
    <>
      <div>
        <PageTitle title="User Point" />
        <SearchInputField
          name="search"
          placeholder="0x...0000"
          onChange={handleSearch}
          defaultValue={queryString.search || ''}
        />
      </div>
      <div
        className="mt-6 overflow-y-auto h-[calc(100vh-203px)] border border-gray-9 bg-white rounded-[28px]"
        onScroll={handleScroll}
      >
        <div className="flex flex-col p-7">{renderList()}</div>
      </div>
    </>
  );
};

export default UserPoint;
